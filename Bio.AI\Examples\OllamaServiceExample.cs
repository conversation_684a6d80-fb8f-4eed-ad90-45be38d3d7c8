using System;
using System.Threading.Tasks;
using Bio.AI.Internal.Services;

namespace Bio.AI.Examples
{
    public static class OllamaServiceExample
    {
        public static async Task RunExample()
        {
            using var service = new OllamaService();
            
            Console.WriteLine("=== 非流式调用示例 ===");
            var result = await service.ChatAsync(
                "什么是人工智能？", 
                "你是一个专业的AI助手", 
                true);
            
            Console.WriteLine($"推理过程: {result.Reasoning}");
            Console.WriteLine($"答案: {result.Answer}");
            
            Console.WriteLine("\n=== 流式调用示例 ===");
            Console.WriteLine("实时输出（回调）：");
            
            await foreach (var chatResult in service.ChatStreamAsync(
                "简单介绍一下机器学习", 
                null, 
                false,
                onTokenReceived: chunk => 
                {
                    // 实时UI更新 - 打字机效果
                    Console.Write($"{chunk.Content}");
                }))
            {
                // 流式处理完成，显示统计信息
                Console.WriteLine("\n\n=== 性能统计 ===");
                Console.WriteLine($"输入tokens: {chatResult.InputTokens}");
                Console.WriteLine($"输出tokens: {chatResult.OutputTokens}");
                Console.WriteLine($"推理tokens: {chatResult.ReasoningTokens}");
                Console.WriteLine($"答案tokens: {chatResult.AnswerTokens}");
                Console.WriteLine($"首token延时: {chatResult.FirstTokenLatency}ms");
                Console.WriteLine($"总耗时: {chatResult.TotalDuration.TotalSeconds:F2}秒");
                Console.WriteLine($"平均速度: {chatResult.TokensPerSecond:F2} tokens/秒");
                
                if (!string.IsNullOrEmpty(chatResult.Reasoning))
                {
                    Console.WriteLine($"\n推理过程: {chatResult.Reasoning}");
                }
                Console.WriteLine($"最终答案: {chatResult.Answer}");
            }
        }
    }
} 