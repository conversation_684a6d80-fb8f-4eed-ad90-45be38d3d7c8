using System;

namespace Bio.AI.Models
{
    /// <summary>
    /// 流式输出的数据块类型
    /// </summary>
    public enum ChunkType
    {
        /// <summary>
        /// 未知类型，初始状态或无法判断
        /// </summary>
        Unknown,
        
        /// <summary>
        /// 推理过程内容
        /// </summary>
        Reasoning,
        
        /// <summary>
        /// 答案内容
        /// </summary>
        Answer
    }

    /// <summary>
    /// 流式输出的数据块
    /// </summary>
    public class StreamChunk
    {
        /// <summary>
        /// 内容文本
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 数据块类型
        /// </summary>
        public ChunkType Type { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public StreamChunk()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="type">类型</param>
        public StreamChunk(string content, ChunkType type)
        {
            Content = content;
            Type = type;
        }
    }
} 