using System.ComponentModel;

namespace Bio.AI.Models
{
    /// <summary>
    /// Ollama本地模型配置
    /// </summary>
    public static class LLModel
    {
        /// <summary>
        /// Ollama本地模型配置
        /// </summary>
        public static class Ollama
        {
            public static readonly LLMProviderConfig DeepSeekR1 = new LLMProviderConfig
            {
                Name = "deepseek-r1:latest",
                Url = "http://localhost:11434",
                ApiKey = "", // Ollama本地不需要API密钥
                Provider = "Ollama"
            };

            public static readonly LLMProviderConfig Qwen3_8B = new LLMProviderConfig
            {
                Name = "qwen3:8b-q8_0",
                Url = "http://localhost:11434",
                ApiKey = "", // Ollama本地不需要API密钥
                Provider = "Ollama"
            };

            public static readonly LLMProviderConfig Qwen25_14B = new LLMProviderConfig
            {
                Name = "qwen2.5:14b",
                Url = "http://localhost:11434",
                ApiKey = "", // Ollama本地不需要API密钥
                Provider = "Ollama"
            };

            public static readonly LLMProviderConfig Qwen25_Latest = new LLMProviderConfig
            {
                Name = "qwen2.5:latest",
                Url = "http://localhost:11434",
                ApiKey = "", // Ollama本地不需要API密钥
                Provider = "Ollama"
            };
        }
    }

    /// <summary>
    /// LLM提供商配置类
    /// </summary>
    public class LLMProviderConfig
    {
        /// <summary>
        /// 模型名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// API基础URL
        /// </summary>
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// 提供商名称
        /// </summary>
        public string Provider { get; set; } = string.Empty;

        /// <summary>
        /// 获取配置的显示名称
        /// </summary>
        public string DisplayName => $"{Provider}.{Name}";
    }
} 