using System.Text.Json.Serialization;

namespace Bio.AI.Core.Models
{
    /// <summary>
    /// 代表与AI模型的聊天消息。
    /// </summary>
    public record ChatMessage
    {
        /// <summary>
        /// 消息的角色 (例如, "system", "user", "assistant")。
        /// </summary>
        [JsonPropertyName("role")]
        public required string Role { get; init; }

        /// <summary>
        /// 消息内容。
        /// </summary>
        [JsonPropertyName("content")]
        public required string Content { get; init; }

        // 可以根据需要添加其他字段，例如 Name (用于 function calling)
    }
}
