using System.Threading;

namespace Bio.AI.Core.Models
{
    /// <summary>
    /// 聊天选项，用于配置LLM请求的参数
    /// </summary>
    public class ChatOptions
    {
        /// <summary>
        /// 重试次数
        /// </summary>
        public int Retries { get; set; } = 3;

        /// <summary>
        /// 取消令牌
        /// </summary>
        public CancellationToken CancellationToken { get; set; } = default;

        /// <summary>
        /// 温度参数，控制输出的随机性
        /// </summary>
        public float? Temperature { get; set; }

        /// <summary>
        /// Top-p采样参数
        /// </summary>
        public float? TopP { get; set; }

        /// <summary>
        /// 最大生成令牌数
        /// </summary>
        public int? MaxTokens { get; set; }
    }
}
