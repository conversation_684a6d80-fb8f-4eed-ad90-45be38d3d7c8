using System.Diagnostics;
using System.Text;
using System.Text.Json;
using Bio.AI.Models;

namespace Bio.AI.Internal.Services
{
    /// <summary>
    /// Ollama LLM服务实现
    /// </summary>
    internal class OllamaService : ILLMService
    {
        private const string ThinkStartTag = "<think>";
        private const string ThinkEndTag = "</think>";
        private const string NoThinkPrefix = "/no_think ";
        private const string ApiChatEndpoint = "/api/chat";
        private const double DefaultTemperature = 0.7;
        private static readonly TimeSpan DefaultTimeout = TimeSpan.FromMinutes(10);

        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _model;
        private bool _disposed = false;

        public OllamaService(string baseUrl = "http://localhost:11434", string model = "qwen3:8b-q8_0")
        {
            _httpClient = new HttpClient { Timeout = DefaultTimeout };
            _baseUrl = baseUrl.TrimEnd('/');
            _model = model;
        }

        public async Task<(string Reasoning, string Answer)> ChatAsync(
            string prompt, 
            string? systemPrompt, 
            bool useReasoningMode)
        {
            var request = CreateHttpRequest(prompt, systemPrompt, useReasoningMode, stream: false);
            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);
            var fullContent = responseData.GetProperty("message").GetProperty("content").GetString() ?? "";

            return ParseResponse(fullContent, useReasoningMode);
        }

        public async IAsyncEnumerable<ChatResult> ChatStreamAsync(
            string prompt, 
            string? systemPrompt, 
            bool useReasoningMode,
            CancellationToken cancellationToken = default,
            Action<StreamChunk>? onTokenReceived = null)
        {
            var startTime = Stopwatch.StartNew();
            var stats = new ChatStats
            {
                InputTokens = CalculateInputTokens(prompt, systemPrompt),
                StartTime = startTime
            };

            var request = CreateHttpRequest(prompt, systemPrompt, useReasoningMode, stream: true);
            using var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
            response.EnsureSuccessStatusCode();

            var (reasoning, answer) = await ProcessStreamResponse(response, useReasoningMode, stats, onTokenReceived, cancellationToken);
            
            yield return BuildFinalResult(startTime, stats, reasoning, answer);
        }

        private HttpRequestMessage CreateHttpRequest(string prompt, string? systemPrompt, bool useReasoningMode, bool stream)
        {
            var requestData = new
            {
                model = _model,
                messages = BuildMessages(prompt, systemPrompt, useReasoningMode),
                stream = stream,
                options = new { temperature = DefaultTemperature }
            };

            var json = JsonSerializer.Serialize(requestData);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            return new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}{ApiChatEndpoint}")
            {
                Content = content
            };
        }

        private static async Task<(string reasoning, string answer)> ProcessStreamResponse(
            HttpResponseMessage response, 
            bool useReasoningMode, 
            ChatStats stats, 
            Action<StreamChunk>? onTokenReceived,
            CancellationToken cancellationToken)
        {
            using var stream = await response.Content.ReadAsStreamAsync(cancellationToken);
            using var reader = new StreamReader(stream, Encoding.UTF8, false, 1024);

            var currentState = ChunkType.Unknown;
            var reasoningBuilder = new StringBuilder();
            var answerBuilder = new StringBuilder();
            
            while (!reader.EndOfStream && !cancellationToken.IsCancellationRequested)
            {
                var line = await reader.ReadLineAsync();
                if (string.IsNullOrEmpty(line)) continue;

                var chunk = ProcessStreamLine(line, ref currentState, useReasoningMode, stats);
                if (chunk != null)
                {
                    onTokenReceived?.Invoke(chunk);
                    
                    if (chunk.Type == ChunkType.Reasoning)
                        reasoningBuilder.Append(chunk.Content);
                    else if (chunk.Type == ChunkType.Answer)
                        answerBuilder.Append(chunk.Content);
                }
            }

            return (reasoningBuilder.ToString(), answerBuilder.ToString());
        }

        private static ChatResult BuildFinalResult(Stopwatch startTime, ChatStats stats, string reasoning, string answer)
        {
            var totalDuration = startTime.Elapsed;
            return new ChatResult
            {
                InputTokens = stats.InputTokens,
                OutputTokens = stats.OutputTokens,
                ReasoningTokens = reasoning.Length,
                AnswerTokens = answer.Length,
                FirstTokenLatency = stats.FirstTokenLatency,
                TotalDuration = totalDuration,
                TokensPerSecond = totalDuration.TotalSeconds > 0 ? stats.OutputTokens / totalDuration.TotalSeconds : 0,
                Reasoning = reasoning,
                Answer = answer
            };
        }

        private static StreamChunk? ProcessStreamLine(string line, ref ChunkType currentState, bool useReasoningMode, ChatStats stats)
        {
            try
            {
                var data = JsonSerializer.Deserialize<JsonElement>(line);
                if (!data.TryGetProperty("message", out var message) || 
                    !message.TryGetProperty("content", out var contentProp))
                    return null;

                var token = contentProp.GetString() ?? "";
                if (string.IsNullOrEmpty(token)) return null;

                UpdateStats(stats, token);
                currentState = UpdateChunkType(currentState, token, useReasoningMode);
                
                return new StreamChunk(token, currentState);
            }
            catch (JsonException)
            {
                return null;
            }
        }

        private static void UpdateStats(ChatStats stats, string token)
        {
            if (!stats.FirstTokenReceived)
            {
                stats.FirstTokenLatency = (int)stats.StartTime.ElapsedMilliseconds;
                stats.FirstTokenReceived = true;
            }
            stats.OutputTokens += token.Length;
        }

        private static int CalculateInputTokens(string prompt, string? systemPrompt) =>
            prompt.Length + (systemPrompt?.Length ?? 0);

        private static object[] BuildMessages(string prompt, string? systemPrompt, bool useReasoningMode)
        {
            var messages = new List<object>();

            if (!string.IsNullOrEmpty(systemPrompt))
                messages.Add(new { role = "system", content = systemPrompt });

            var userContent = useReasoningMode ? prompt : $"{NoThinkPrefix}{prompt}";
            messages.Add(new { role = "user", content = userContent });

            return messages.ToArray();
        }

        private static (string Reasoning, string Answer) ParseResponse(string fullContent, bool useReasoningMode)
        {
            if (!useReasoningMode)
                return ("", fullContent);

            var thinkStart = fullContent.IndexOf(ThinkStartTag, StringComparison.OrdinalIgnoreCase);
            var thinkEnd = fullContent.IndexOf(ThinkEndTag, StringComparison.OrdinalIgnoreCase);

            if (thinkStart >= 0 && thinkEnd > thinkStart)
            {
                var reasoning = fullContent.Substring(thinkStart + ThinkStartTag.Length, thinkEnd - thinkStart - ThinkStartTag.Length).Trim();
                var answer = fullContent.Substring(thinkEnd + ThinkEndTag.Length).Trim();
                return (reasoning, answer);
            }

            return ("", fullContent);
        }

        private static ChunkType UpdateChunkType(ChunkType currentState, string token, bool useReasoningMode)
        {
            if (!useReasoningMode)
                return ChunkType.Answer;

            if (currentState == ChunkType.Unknown && token.Contains(ThinkStartTag))
                return ChunkType.Reasoning;
            
            if (currentState == ChunkType.Reasoning && token.Contains(ThinkEndTag))
                return ChunkType.Answer;

            return currentState == ChunkType.Unknown ? ChunkType.Unknown : currentState;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _httpClient?.Dispose();
                _disposed = true;
            }
        }

        /// <summary>
        /// 内部统计数据类
        /// </summary>
        private class ChatStats
        {
            public int InputTokens { get; set; }
            public int OutputTokens { get; set; }
            public int FirstTokenLatency { get; set; }
            public bool FirstTokenReceived { get; set; }
            public Stopwatch StartTime { get; set; } = new();
        }
    }
} 