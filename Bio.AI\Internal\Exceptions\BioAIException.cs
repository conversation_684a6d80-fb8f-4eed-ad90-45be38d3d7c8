using System;

namespace Bio.AI.Internal.Exceptions
{
    /// <summary>
    /// BioAI专用异常类
    /// </summary>
    internal class BioAIException : Exception
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">异常消息</param>
        public BioAIException(string message) : base(message)
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">异常消息</param>
        /// <param name="innerException">内部异常</param>
        public BioAIException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }
} 