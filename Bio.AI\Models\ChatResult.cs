using System;

namespace Bio.AI.Models
{
    /// <summary>
    /// 聊天完成结果，包含性能统计信息
    /// </summary>
    public class ChatResult
    {
        /// <summary>
        /// 输入tokens数量（按字符数估算）
        /// </summary>
        public int InputTokens { get; set; }

        /// <summary>
        /// 输出tokens总数（按字符数计算）
        /// </summary>
        public int OutputTokens { get; set; }

        /// <summary>
        /// 推理部分tokens数量
        /// </summary>
        public int ReasoningTokens { get; set; }

        /// <summary>
        /// 答案部分tokens数量
        /// </summary>
        public int AnswerTokens { get; set; }

        /// <summary>
        /// 首token延时（毫秒）
        /// </summary>
        public int FirstTokenLatency { get; set; }

        /// <summary>
        /// 总耗时
        /// </summary>
        public TimeSpan TotalDuration { get; set; }

        /// <summary>
        /// 平均tokens每秒
        /// </summary>
        public double TokensPerSecond { get; set; }

        /// <summary>
        /// 推理内容
        /// </summary>
        public string Reasoning { get; set; } = string.Empty;

        /// <summary>
        /// 答案内容
        /// </summary>
        public string Answer { get; set; } = string.Empty;
    }
} 