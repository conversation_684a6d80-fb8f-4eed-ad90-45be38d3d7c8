using System;
using System.Threading.Tasks;
using Bio.AI.Models;
using Bio.AI.Examples;

namespace Bio.AI
{
    /// <summary>
    /// 测试模式枚举
    /// </summary>
    public enum TestMode
    {
        ReasoningWithStream = 1,    // 推理模式 + 流式输出
        ReasoningWithoutStream = 2, // 推理模式 + 非流式输出
        NormalWithStream = 3,       // 普通模式 + 流式输出
        NormalWithoutStream = 4     // 普通模式 + 非流式输出
    }

    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== Bio.AI Ollama 本地模型测试 ===\n");

            // 显示Ollama状态
            OllamaExample.ShowOllamaStatus();
            
            // 用户选择测试模式
            var testMode = GetUserChoice();
            
            Console.WriteLine("\n按任意键开始测试Ollama模型...");
            Console.ReadKey();
            Console.WriteLine("\n");

            await TestOllamaModels(testMode);

            Console.WriteLine("\n按任意键退出程序...");
            Console.ReadKey();
        }

        static TestMode GetUserChoice()
        {
            Console.WriteLine("\n=== 选择测试模式 ===");
            Console.WriteLine("1. 推理模式 + 流式输出");
            Console.WriteLine("2. 推理模式 + 非流式输出");
            Console.WriteLine("3. 普通模式 + 流式输出");
            Console.WriteLine("4. 普通模式 + 非流式输出");
            Console.Write("\n请选择模式 (1-4): ");

            while (true)
            {
                var input = Console.ReadLine();
                if (int.TryParse(input, out int choice) && choice >= 1 && choice <= 4)
                {
                    var mode = (TestMode)choice;
                    Console.WriteLine($"已选择: {GetModeDescription(mode)}");
                    return mode;
                }
                Console.Write("无效选择，请输入 1-4: ");
            }
        }

        static string GetModeDescription(TestMode mode)
        {
            return mode switch
            {
                TestMode.ReasoningWithStream => "推理模式 + 流式输出",
                TestMode.ReasoningWithoutStream => "推理模式 + 非流式输出",
                TestMode.NormalWithStream => "普通模式 + 流式输出",
                TestMode.NormalWithoutStream => "普通模式 + 非流式输出",
                _ => "未知模式"
            };
        }

        static async Task TestOllamaModels(TestMode testMode)
        {
            Console.WriteLine("=== 测试 Ollama 本地模型 ===");

            var useReasoningMode = testMode == TestMode.ReasoningWithStream || testMode == TestMode.ReasoningWithoutStream;
            var useStreamMode = testMode == TestMode.ReasoningWithStream || testMode == TestMode.NormalWithStream;

            try
            {
                Console.WriteLine($"\n正在调用当前运行的模型: {LLModel.Ollama.Qwen3_8B.Name}");
                Console.WriteLine($"使用模式: {GetModeDescription(testMode)}");
                
                var prompt = "请用中文介绍一下你自己";
                var systemPrompt = "你是一个友好的AI助手，请用简洁的语言回答";

                if (useStreamMode)
                {
                    Console.WriteLine("\n流式输出结果:");
                    Console.ForegroundColor = ConsoleColor.Cyan;
                    Console.WriteLine("🔄 实时输出:");
                    
                    // 添加token回调演示
                    int tokenCount = 0;
                    
                    await foreach (var result in BioAI.ThinkStreamAsync(prompt, LLModel.Ollama.Qwen3_8B, useReasoningMode, systemPrompt, 
                        onTokenReceived: token => 
                        {
                            Console.Write(token.Content); // 实时打印token内容
                            tokenCount++;
                            if (tokenCount % 10 == 0) // 每10个token显示一次统计
                            {
                                Console.Title = $"Bio.AI - 已接收 {tokenCount} 个tokens";
                            }
                        }))
                    {
                        // 流式处理完成，显示最终统计和结果
                        Console.ResetColor();
                        Console.WriteLine("\n\n=== 流式处理完成 ===");
                        
                        // 显示推理过程（如果有）
                        if (!string.IsNullOrEmpty(result.Reasoning))
                        {
                            Console.ForegroundColor = ConsoleColor.DarkGray;
                            Console.WriteLine($"\n📚 推理过程 ({result.ReasoningTokens} tokens):\n{result.Reasoning}");
                            Console.ResetColor();
                        }
                        
                        // 显示最终答案
                        Console.ForegroundColor = ConsoleColor.Green;
                        Console.WriteLine($"\n💡 最终答案 ({result.AnswerTokens} tokens):\n{result.Answer}");
                        Console.ResetColor();
                        
                        // 显示性能统计
                        Console.ForegroundColor = ConsoleColor.Yellow;
                        Console.WriteLine("\n📊 性能统计:");
                        Console.WriteLine($"   输入tokens: {result.InputTokens}");
                        Console.WriteLine($"   输出tokens: {result.OutputTokens}");
                        Console.WriteLine($"   首token延时: {result.FirstTokenLatency}ms");
                        Console.WriteLine($"   总耗时: {result.TotalDuration.TotalSeconds:F2}秒");
                        Console.WriteLine($"   平均速度: {result.TokensPerSecond:F2} tokens/秒");
                        Console.ResetColor();
                    }
                    
                    Console.Title = "Bio.AI";
                }
                else
                {
                    var (reasoning, answer) = await BioAI.ThinkAsync(prompt, LLModel.Ollama.Qwen3_8B, useReasoningMode, systemPrompt);

                    if (!string.IsNullOrEmpty(reasoning))
                    {
                        Console.ForegroundColor = ConsoleColor.DarkGray;
                        Console.WriteLine($"\n📚 推理过程:\n{reasoning}");
                        Console.ResetColor();
                    }
                    
                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine($"\n💡 最终答案:\n{answer}");
                    Console.ResetColor();
                }
                
                Console.WriteLine("\n✅ Ollama本地模型调用成功！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ Ollama调用失败: {ex.Message}");
                Console.WriteLine("请确保:");
                Console.WriteLine("1. Ollama服务正在运行 (ollama serve)");
                Console.WriteLine("2. 模型已加载 (ollama run qwen3:8b-q8_0)");
                Console.WriteLine("3. 端口11434可访问");
            }
        }
    }
} 