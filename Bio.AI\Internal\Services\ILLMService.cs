using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Bio.AI.Models;

namespace Bio.AI.Internal.Services
{
    /// <summary>
    /// LLM服务接口，内部使用
    /// </summary>
    internal interface ILLMService : IDisposable
    {
        /// <summary>
        /// 执行非流式聊天补全，直接返回分离后的推理和答案
        /// </summary>
        /// <param name="prompt">用户问题</param>
        /// <param name="systemPrompt">系统提示词，可选</param>
        /// <param name="useReasoningMode">是否使用推理模式</param>
        /// <returns>推理过程和答案</returns>
        Task<(string Reasoning, string Answer)> ChatAsync(
            string prompt, 
            string? systemPrompt, 
            bool useReasoningMode);

        /// <summary>
        /// 执行流式聊天补全，返回最终统计结果
        /// </summary>
        /// <param name="prompt">用户问题</param>
        /// <param name="systemPrompt">系统提示词，可选</param>
        /// <param name="useReasoningMode">是否使用推理模式</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <param name="onTokenReceived">token接收回调</param>
        /// <returns>聊天完成结果</returns>
        IAsyncEnumerable<ChatResult> ChatStreamAsync(
            string prompt, 
            string? systemPrompt, 
            bool useReasoningMode,
            CancellationToken cancellationToken = default,
            Action<StreamChunk>? onTokenReceived = null);
    }
} 