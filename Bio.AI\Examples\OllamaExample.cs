using System;
using System.Threading.Tasks;
using Bio.AI.Models;

namespace Bio.AI.Examples
{
    /// <summary>
    /// Ollama本地模型使用示例
    /// </summary>
    public class OllamaExample
    {
        /// <summary>
        /// 演示如何使用BioAI调用Ollama本地模型
        /// </summary>
        public static async Task RunOllamaExample()
        {
            Console.WriteLine("=== Bio.AI Ollama 本地模型测试 ===\n");

            Console.WriteLine("测试当前运行的模型: qwen3:8b-q8_0\n");

            try
            {
                Console.WriteLine("正在调用 LLModel.Ollama.Qwen3_8B...");
                
                var (reasoning, answer) = await BioAI.ThinkAsync(
                    "请用中文简单介绍一下你自己", 
                    LLModel.Ollama.Qwen3_8B,
                    useReasoningMode: false,
                    systemPrompt: "你是一个友好的AI助手，请用简洁的语言回答"
                );

                Console.WriteLine($"\n最终答案:\n{answer}");
                
                Console.WriteLine("\n✅ Ollama模型调用成功！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ Ollama调用失败: {ex.Message}");
                Console.WriteLine("请确保Ollama服务正在运行，并且模型已加载。");
            }

            Console.WriteLine("\n" + new string('-', 50));
            
            // 测试其他模型
            Console.WriteLine("\n测试其他可用模型:\n");
            
            var models = new[]
            {
                ("DeepSeek R1", LLModel.Ollama.DeepSeekR1),
                ("Qwen2.5 14B", LLModel.Ollama.Qwen25_14B),
                ("Qwen2.5 Latest", LLModel.Ollama.Qwen25_Latest)
            };

            foreach (var (modelName, config) in models)
            {
                Console.WriteLine($"测试 {modelName} ({config.Name}):");
                try
                {
                    var (reasoning, answer) = await BioAI.ThinkAsync(
                        "1+1等于几？", 
                        config,
                        useReasoningMode: false,
                        systemPrompt: "请简单回答数学问题"
                    );

                    Console.WriteLine($"  答案: {answer}");
                    Console.WriteLine($"  ✅ {modelName} 可用");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  ❌ {modelName} 不可用: {ex.Message}");
                }
                Console.WriteLine();
            }
        }

        /// <summary>
        /// 展示Ollama模型状态
        /// </summary>
        public static void ShowOllamaStatus()
        {
            Console.WriteLine("=== Ollama 模型状态 ===\n");
            Console.WriteLine("根据您的ollama ps和ollama list输出：\n");
            
            Console.WriteLine("当前运行的模型:");
            Console.WriteLine("  🟢 qwen3:8b-q8_0 (11 GB, 100% GPU)\n");
            
            Console.WriteLine("可用的模型:");
            Console.WriteLine("  📦 deepseek-r1:latest (4.7 GB)");
            Console.WriteLine("  📦 qwen3:8b-q8_0 (8.9 GB) - 当前运行");
            Console.WriteLine("  📦 qwen2.5:14b (9.0 GB)");
            Console.WriteLine("  📦 qwen2.5:latest (4.7 GB)\n");
            
            Console.WriteLine("配置的枚举:");
            Console.WriteLine("  - LLModel.Ollama.DeepSeekR1");
            Console.WriteLine("  - LLModel.Ollama.Qwen3_8B ⭐ 推荐(当前运行)");
            Console.WriteLine("  - LLModel.Ollama.Qwen25_14B");
            Console.WriteLine("  - LLModel.Ollama.Qwen25_Latest");
        }
    }
} 