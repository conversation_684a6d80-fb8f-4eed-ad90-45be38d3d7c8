using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Bio.AI.Internal;
using Bio.AI.Internal.Exceptions;
using Bio.AI.Models;
using System.Threading;

namespace Bio.AI
{
    /// <summary>
    /// BioAI主入口类，提供AI能力的统一访问接口
    /// </summary>
    public static class BioAI
    {
        /// <summary>
        /// 获取带推理过程的回答
        /// </summary>
        /// <param name="prompt">用户问题</param>
        /// <param name="model">模型配置</param>
        /// <param name="useReasoningMode">是否使用推理模式</param>
        /// <param name="systemPrompt">系统提示词，可选</param>
        /// <returns>推理过程和答案</returns>
        /// <exception cref="BioAIException">AI调用失败时抛出</exception>
        public static async Task<(string Reasoning, string Answer)> ThinkAsync(
            string prompt, 
            LLMProviderConfig model, 
            bool useReasoningMode = false, 
            string? systemPrompt = null)
        {
            if (string.IsNullOrWhiteSpace(prompt))
                throw new ArgumentException("问题不能为空", nameof(prompt));
            
            if (model == null)
                throw new ArgumentNullException(nameof(model));

            try
            {
                Logger.Info($"开始AI推理任务: {model.DisplayName}, 推理模式: {useReasoningMode}");
                
                // 获取或创建服务实例（延迟初始化）
                var service = ServiceManager.GetOrCreateService(model);
                
                // 执行推理任务
                var result = await service.ChatAsync(prompt, systemPrompt, useReasoningMode);
                
                Logger.Info($"AI推理任务完成: {model.DisplayName}");
                return result;
            }
            catch (BioAIException)
            {
                // BioAI异常直接传播
                throw;
            }
            catch (Exception ex)
            {
                Logger.Error($"AI推理任务失败: {model.DisplayName}", ex);
                throw new BioAIException($"AI推理失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 流式获取回答
        /// </summary>
        /// <param name="prompt">用户问题</param>
        /// <param name="model">模型配置</param>
        /// <param name="useReasoningMode">是否使用推理模式</param>
        /// <param name="systemPrompt">系统提示词，可选</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <param name="onTokenReceived">token接收回调</param>
        /// <returns>聊天完成结果</returns>
        /// <exception cref="BioAIException">AI调用失败时抛出</exception>
        public static async IAsyncEnumerable<ChatResult> ThinkStreamAsync(
            string prompt, 
            LLMProviderConfig model, 
            bool useReasoningMode = false, 
            string? systemPrompt = null,
            [System.Runtime.CompilerServices.EnumeratorCancellation] CancellationToken cancellationToken = default,
            Action<StreamChunk>? onTokenReceived = null)
        {
            if (string.IsNullOrWhiteSpace(prompt))
                throw new ArgumentException("问题不能为空", nameof(prompt));
            
            if (model == null)
                throw new ArgumentNullException(nameof(model));

            Logger.Info($"开始AI流式推理任务: {model.DisplayName}, 推理模式: {useReasoningMode}");

            // 获取或创建服务实例（延迟初始化）
            var service = ServiceManager.GetOrCreateService(model);

            // 执行流式推理任务
            await foreach (var chunk in service.ChatStreamAsync(prompt, systemPrompt, useReasoningMode, cancellationToken, onTokenReceived))
            {
                yield return chunk;
            }

            Logger.Info($"AI流式推理任务完成: {model.DisplayName}");
        }
    }
} 