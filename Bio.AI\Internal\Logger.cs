using System;
using System.Diagnostics;

namespace Bio.AI.Internal
{
    /// <summary>
    /// 内置日志系统，根据运行环境自动选择输出方式
    /// </summary>
    internal static class Logger
    {
        private static readonly Action<string, string> _logAction;

        static Logger()
        {
            try
            {
                // 尝试检测是否有控制台输出
                var standardOutput = Console.OpenStandardOutput();
                if (standardOutput != null)
                {
                    // Console/Server应用，使用Console输出
                    _logAction = (level, msg) => Console.WriteLine($"[BioAI {level}] {msg}");
                }
                else
                {
                    // GUI应用（WinForm/WPF等），使用Debug输出
                    _logAction = (level, msg) => Debug.WriteLine($"[BioAI {level}] {msg}");
                }
            }
            catch
            {
                // 如果检测失败，默认使用Debug输出
                _logAction = (level, msg) => Debug.WriteLine($"[BioAI {level}] {msg}");
            }
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        public static void Info(string message)
        {
            _logAction("INFO", message);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        public static void Warning(string message)
        {
            _logAction("WARN", message);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        public static void Error(string message)
        {
            _logAction("ERROR", message);
        }

        /// <summary>
        /// 记录错误日志（包含异常信息）
        /// </summary>
        public static void Error(string message, Exception exception)
        {
            _logAction("ERROR", $"{message}: {exception.Message}");
        }
    }
} 