@echo off
echo 开始打包Bio.AI类库...

REM 清理旧的构建文件
dotnet clean -c Release

REM 构建Release版本
dotnet build -c Release

REM 创建NuGet包
dotnet pack -c Release -o ./nupkgs

echo NuGet包已创建在 ./nupkgs 目录下

REM 发布到本地NuGet服务器
echo 是否要发布到本地NuGet服务器(http://222.128.24.148:5555)? (Y/N)
set /p publish=

if /i "%publish%"=="Y" (
    echo 请输入NuGet API Key:
    set /p apikey=
    dotnet nuget push ./nupkgs/Bio.AI.1.0.0.nupkg --source http://222.128.24.148:5555/v3/index.json --api-key sk-83203af0481445bc970986fee4149e28
    echo 发布完成!
) else (
    echo 跳过发布步骤。
)

echo 打包过程完成。
pause
