using System;
using System.Collections.Concurrent;
using System.Net.Http;
using Bio.AI.Internal.Exceptions;
using Bio.AI.Internal.Services;
using Bio.AI.Models;

namespace Bio.AI.Internal
{
    /// <summary>
    /// 服务管理器，负责创建和管理LLM服务实例
    /// </summary>
    internal static class ServiceManager
    {
        private static readonly ConcurrentDictionary<string, ILLMService> _services = new();
        private static readonly ConcurrentDictionary<string, HttpClient> _httpClients = new();

        static ServiceManager()
        {
            Logger.Info("BioAI ServiceManager 初始化");
            
            // 注册应用程序域退出事件，自动清理资源
            AppDomain.CurrentDomain.ProcessExit += CleanupResources;
        }

        /// <summary>
        /// 获取或创建LLM服务实例（线程安全）
        /// </summary>
        /// <param name="config">LLM配置</param>
        /// <returns>LLM服务实例</returns>
        public static ILLMService GetOrCreateService(LLMProviderConfig config)
        {
            return _services.GetOrAdd(config.DisplayName, _ => 
            {
                Logger.Info($"创建服务实例: {config.DisplayName}");
                return CreateService(config);
            });
        }

        /// <summary>
        /// 创建LLM服务实例
        /// </summary>
        private static ILLMService CreateService(LLMProviderConfig config)
        {
            try
            {
                // 每个模型使用独立的HttpClient
                var httpClient = _httpClients.GetOrAdd(config.DisplayName, _ => 
                {
                    Logger.Info($"创建HttpClient: {config.DisplayName}");
                    return new HttpClient();
                });
                
                // 根据提供商类型创建相应的服务
                return config.Provider.ToLower() switch
                {
                    "ollama" => new OllamaService(config.Url, config.Name),
                    _ => throw new BioAIException($"不支持的模型提供商: {config.Provider}")
                };
            }
            catch (Exception ex)
            {
                Logger.Error($"创建服务失败: {config.DisplayName}", ex);
                throw new BioAIException($"无法创建模型服务 {config.DisplayName}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 清理所有资源
        /// </summary>
        private static void CleanupResources(object? sender, EventArgs e)
        {
            Logger.Info("清理BioAI资源");
            
            try
            {
                // 清理所有HttpClient
                foreach (var client in _httpClients.Values)
                {
                    client?.Dispose();
                }
                _httpClients.Clear();

                // 清理所有服务
                foreach (var service in _services.Values)
                {
                    service?.Dispose();
                }
                _services.Clear();
                
                Logger.Info("BioAI资源清理完成");
            }
            catch (Exception ex)
            {
                Logger.Error("清理资源时发生错误", ex);
            }
        }
    }
} 